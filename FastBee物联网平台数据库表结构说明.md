# FastBee物联网平台数据库表结构说明

## 系统管理相关表

### 0. 语言表 (app_language)

语言表存储系统支持的语言信息。

主要字段：

- `id`：语言ID，主键
- `language`：语言
- `country`：国家
- `timezone`：时区
- `create_by`：创建者
- `create_time`：创建时间
- `language_name`：语言名称

### 0. 用户偏好设置表 (app_preferences)

用户偏好设置表存储用户的个性化设置信息。

主要字段：

- `user_id`：用户ID，主键
- `language`：语言
- `timezone`：时区
- `create_by`：创建者
- `create_time`：创建时间
- `update_by`：更新者
- `update_time`：更新时间
- `remark`：备注

### 0. 桥接表 (bridge)

桥接表存储系统桥接配置信息。

主要字段：

- `id`：ID，主键
- `config`：配置信息
- `name`：名称
- `enable`：是否生效
- `status`：状态
- `type`：类型
- `direction`：方向
- `route`：路由
- `del_flag`：删除标志
- `create_by`：创建者
- `create_time`：创建时间
- `update_by`：更新者
- `update_time`：更新时间
- `remark`：备注
- `tenant_id`：租户ID
- `tenant_name`：租户名称

### 0. 指令偏好设置表 (command_preferences)

指令偏好设置表存储用户的指令偏好设置。

主要字段：

- `command_id`：指令ID，主键
- `command_name`：指令名称
- `command`：指令
- `serial_number`：设备编号

### 1. 用户表 (sys_user)

用户表存储系统用户的基本信息，包括用户ID、部门ID、用户账号、昵称、邮箱、手机号码、性别、头像、密码、状态等信息。

主要字段：

- `user_id`：用户ID，主键
- `dept_id`：部门ID
- `user_name`：用户账号
- `nick_name`：用户昵称
- `email`：用户邮箱
- `phonenumber`：手机号码
- `sex`：用户性别
- `avatar`：头像路径
- `password`：密码
- `status`：帐号状态（0正常 1停用）
- `del_flag`：删除标志（0存在 2删除）
- `login_ip`：最后登录IP
- `login_date`：最后登录时间

### 2. 角色表 (sys_role)

角色表存储系统角色信息，用于权限管理。

主要字段：

- `role_id`：角色ID，主键
- `role_name`：角色名称
- `role_key`：角色权限字符串
- `role_sort`：显示顺序
- `data_scope`：数据范围
- `menu_check_strictly`：菜单树选择项是否关联显示
- `dept_check_strictly`：部门树选择项是否关联显示
- `status`：角色状态

### 3. 菜单表 (sys_menu)

菜单表存储系统菜单信息，用于构建系统导航和权限控制。

主要字段：

- `menu_id`：菜单ID，主键
- `menu_name`：菜单名称
- `parent_id`：父菜单ID
- `order_num`：显示顺序
- `path`：路由地址
- `component`：组件路径
- `query`：路由参数
- `is_frame`：是否为外链
- `is_cache`：是否缓存
- `menu_type`：菜单类型（M目录 C菜单 F按钮）
- `visible`：菜单状态（0显示 1隐藏）
- `status`：菜单状态（0正常 1停用）
- `perms`：权限标识
- `icon`：菜单图标

### 4. 部门表 (sys_dept)

部门表存储组织机构信息。

主要字段：

- `dept_id`：部门ID，主键
- `dept_system_user_id`：部门系统账号ID
- `parent_id`：父部门ID
- `ancestors`：祖级列表
- `dept_name`：部门名称
- `order_num`：显示顺序
- `leader`：负责人
- `phone`：联系电话
- `status`：部门状态（0正常 1停用）
- `email`：邮箱
- `dept_type`：机构类型
- `dept_logo`：机构logo
- `dept_logo_name`：logo名称

## 物联网设备管理相关表

### 1. 设备表 (iot_device)

设备表存储物联网设备的基本信息。

主要字段：

- `device_id`：设备ID，主键
- `device_name`：设备名称
- `product_id`：产品ID
- `product_name`：产品名称
- `tenant_id`：租户ID
- `tenant_name`：租户名称
- `serial_number`：设备编号
- `gateway_sn`：网关设备码
- `firmware_version`：固件版本
- `status`：设备状态（1-未激活，2-禁用，3-在线，4-离线）
- `rssi`：信号强度
- `is_shadow`：是否启用设备影子（0-否，1-是）
- `location_way`：定位方式
- `things_model_value`：物模型值
- `network_address`：网络地址
- `network_ip`：网络IP
- `longitude`：经度
- `latitude`：纬度
- `active_time`：激活时间
- `img_url`：图片地址

### 2. 产品表 (iot_product)

产品表存储物联网产品的基本信息。

主要字段：

- `product_id`：产品ID，主键
- `product_name`：产品名称
- `protocol_code`：协议编号
- `category_id`：产品分类ID
- `category_name`：产品分类名称
- `tenant_id`：租户ID
- `tenant_name`：租户名称
- `is_sys`：是否系统通用（0-否，1-是）
- `is_authorize`：是否启用授权码（0-否，1-是）
- `mqtt_account`：mqtt账号
- `mqtt_password`：mqtt密码
- `mqtt_secret`：mqtt秘钥
- `status`：状态（1-未发布，2-已发布）
- `things_models`：物模型JSON
- `device_type`：设备类型（1-直连设备、2-网关设备、3-网关子设备）
- `network_method`：联网方式（1-wifi、2-蜂窝(2G/3G/4G/5G)、3-以太网、4-其他）
- `vertificate_method`：认证方式（1-账号密码、2-证书、3-Http）
- `img_url`：图片地址
- `protocol_type`：产品支持的传输协议（1-MQTT、2-COAP、3-HTTP、4-TCP、5-UDP）
- `location_way`：定位方式

### 3. 物模型表 (iot_things_model)

物模型表存储设备的物模型定义。

主要字段：

- `model_id`：物模型ID，主键
- `model_name`：物模型名称
- `product_id`：产品ID
- `product_name`：产品名称
- `tenant_id`：租户ID
- `tenant_name`：租户名称
- `identifier`：标识符，产品下唯一
- `type`：模型类别（1-属性，2-功能，3-事件）
- `datatype`：数据类型（integer、decimal、string、bool、array、enum）
- `specs`：数据定义
- `is_chart`：是否图表展示（0-否，1-是）
- `is_monitor`：是否实时监测（0-否，1-是）
- `is_history`：是否历史存储（0-否，1-是）
- `is_readonly`：是否只读数据（0-否，1-是）
- `is_share_perm`：是否设备分享权限（0-否，1-是）
- `model_order`：排序，值越大，排序越靠前
- `formula`：计算公式
- `is_app`：是否在APP显示（0-否，1-是）

### 4. 物模型模板表 (iot_things_model_template)

物模型模板表存储可复用的物模型模板定义。

主要字段：

- `template_id`：物模型ID，主键
- `template_name`：物模型名称
- `tenant_id`：租户ID
- `tenant_name`：租户名称
- `identifier`：标识符，产品下唯一
- `type`：模型类别（1-属性，2-功能，3-事件）
- `datatype`：数据类型（integer、decimal、string、bool、array、enum）
- `specs`：数据定义
- `is_sys`：是否系统通用（0-否，1-是）
- `is_chart`：是否图表展示（0-否，1-是）
- `is_monitor`：是否实时监测（0-否，1-是）
- `is_history`：是否历史存储（0-否，1-是）
- `is_readonly`：是否只读数据（0-否，1-是）
- `is_share_perm`：是否设备分享权限（0-否，1-是）
- `model_order`：排序，值越大，排序越靠前
- `formula`：计算公式
- `is_app`：是否在APP显示（0-否，1-是）

### 5. 产品分类表 (iot_category)

产品分类表存储产品的分类信息。

主要字段：

- `category_id`：产品分类ID，主键
- `category_name`：产品分类名称
- `tenant_id`：租户ID
- `tenant_name`：租户名称
- `is_sys`：是否系统通用（0-否，1-是）
- `parent_id`：父级ID
- `order_num`：显示顺序

### 6. 设备分组表 (iot_group)

设备分组表存储设备的分组信息。

主要字段：

- `group_id`：分组ID，主键
- `group_name`：分组名称
- `group_order`：分组排序
- `user_id`：用户ID
- `user_name`：用户昵称

### 7. Modbus配置表 (iot_modbus_config)

Modbus配置表存储Modbus协议的配置信息。

主要字段：

- `id`：业务id，主键
- `product_id`：所属产品id
- `identifier`：关联属性
- `slave`：从机地址
- `address`：寄存器地址
- `is_readonly`：是否只读（0-否，1-是）
- `data_type`：modbus数据类型
- `quantity`：读取个数
- `type`：寄存器类型（1-IO寄存器 2-数据寄存器）
- `bit_order`：bit位排序
- `sort`：排序
- `del_flag`：删除标志
- `create_by`：创建者
- `create_time`：创建时间
- `update_by`：更新者
- `update_time`：更新时间
- `remark`：备注

### 8. 设备分享表 (iot_device_share)

设备分享表存储设备分享的信息。

主要字段：

- `device_id`：设备ID，主键
- `user_id`：用户ID，主键
- `phone`：手机号
- `perms`：用户物模型权限
- `del_flag`：删除标志
- `create_by`：创建者
- `create_time`：创建时间
- `update_by`：更新者
- `update_time`：更新时间
- `remark`：备注

### 9. 设备模板表 (iot_device_template)

设备模板表存储设备模板信息。

主要字段：

- `id`：自增ID，主键
- `product_id`：产品ID
- `template_id`：采集点模板ID

### 10. 设备用户关联表 (iot_device_user)

设备用户关联表存储设备与用户的关联信息。

主要字段：

- `device_id`：设备ID，主键
- `user_id`：用户ID，主键
- `phonenumber`：手机号码
- `del_flag`：删除标志
- `create_by`：创建者
- `create_time`：创建时间
- `update_by`：更新者
- `update_time`：更新时间
- `remark`：备注

### 11. 事件日志表 (iot_event_log)

事件日志表存储设备事件的日志信息。

主要字段：

- `event_log_id`：日志ID，主键
- `identifier`：标识符
- `model_name`：物模型名称
- `type`：类型
- `log_value`：日志值
- `device_id`：设备ID
- `device_name`：设备名称
- `serial_number`：设备编号
- `is_monitor`：是否监测数据
- `mode`：模式
- `user_id`：用户ID
- `user_name`：用户名称
- `tenant_id`：租户ID
- `tenant_name`：租户名称
- `create_by`：创建者
- `create_time`：创建时间
- `remark`：备注

### 12. 固件表 (iot_firmware)

固件表存储设备固件信息。

主要字段：

- `firmware_id`：固件ID，主键
- `firmware_name`：固件名称
- `firmware_type`：固件类型
- `product_id`：产品ID
- `product_name`：产品名称
- `tenant_id`：租户ID
- `tenant_name`：租户名称
- `is_sys`：是否系统通用
- `is_latest`：是否最新版本
- `version`：版本
- `file_path`：文件路径
- `del_flag`：删除标志
- `create_by`：创建者
- `create_time`：创建时间
- `update_by`：更新者
- `update_time`：更新时间
- `remark`：备注

### 13. 固件升级任务表 (iot_firmware_task)

固件升级任务表存储固件升级任务信息。

主要字段：

- `id`：主键
- `task_name`：任务名称
- `firmware_id`：关联固件ID
- `upgrade_type`：升级类型
- `task_desc`：任务描述
- `device_count`：选中设备总数
- `del_flag`：删除标志
- `update_time`：更新时间
- `create_time`：创建时间
- `scheduled_time`：预定时间升级

### 14. 固件升级任务详情表 (iot_firmware_task_detail)

固件升级任务详情表存储固件升级任务的详细信息。

主要字段：

- `id`：主键
- `task_id`：任务ID
- `device_sn`：设备编码
- `status`：升级状态
- `desc`：描述
- `create_time`：创建时间
- `message_id`：消息ID
- `update_time`：更新时间

### 15. 设备功能日志表 (iot_function_log)

设备功能日志表存储设备功能的日志信息。

主要字段：

- `function_log_id`：设备功能日志ID，主键
- `identifier`：标识符
- `function_type`：功能类型
- `function_value`：功能值
- `message_id`：消息ID
- `device_name`：设备名称
- `serial_number`：设备编号
- `mode`：模式
- `user_id`：用户ID
- `result_msg`：下发结果描述
- `result_code`：下发结果代码
- `create_by`：创建者
- `create_time`：创建时间
- `remark`：备注
- `display_value`：显示值
- `model_name`：物模型名称
- `reply_time`：设备回复时间

## 场景联动相关表

### 1. 场景表 (iot_scene)

场景表存储场景联动的基本信息。

主要字段：

- `scene_id`：场景ID，主键
- `scene_name`：场景名称
- `chain_name`：规则名称
- `enable`：场景状态（1-启动，2-停止）
- `user_id`：用户ID
- `user_name`：用户名称
- `silent_period`：静默周期（分钟）
- `cond`：执行条件（1=或、任意条件，2=且、所有条件，3=非，不满足）
- `execute_mode`：执行方式（1=串行，顺序执行，2=并行，同时执行）
- `execute_delay`：延时执行（秒钟）
- `has_alert`：是否包含告警推送（1=包含，2=不包含）
- `application_name`：应用名称
- `el_data`：规则数据
- `terminal_user`：是否终端用户（1-是，0-不是）
- `check_delay`：延时匹配（秒钟）
- `recover_id`：恢复告警场景ID

### 2. 场景设备表 (iot_scene_device)

场景设备表存储场景中使用的设备信息。

主要字段：

- `scene_device_id`：场景设备ID，主键
- `serial_number`：设备编号（产品触发的没有设备编号）
- `product_id`：产品ID
- `product_name`：产品名称
- `source`：触发源（1=设备触发，3=产品触发）
- `scene_id`：场景ID
- `script_id`：场景脚本ID
- `type`：类型（2=触发器，3=执行动作）

### 3. 场景脚本表 (iot_scene_script)

场景脚本表存储场景中使用的脚本信息。

主要字段：

- `script_id`：脚本ID，主键
- `scene_id`：场景ID
- `source`：触发源（1=设备触发，2=定时触发，3=产品触发,4=告警执行）
- `script_purpose`：脚本用途（1=数据流，2=触发器，3=执行动作）
- `product_id`：产品ID（用于获取对应物模型）
- `product_name`：产品名称
- `id`：物模型标识符
- `name`：物模型名称
- `value`：物模型值
- `operator`：操作符
- `type`：物模型类别（1=属性，2=功能，3=事件，4=设备升级，5=设备上线，6=设备下线）
- `device_count`：设备数量
- `job_id`：任务ID
- `cron_expression`：cron执行表达式
- `is_advance`：是否详细corn表达式（1=是，0=否）

### 4. 规则引擎脚本表 (iot_script)

规则引擎脚本表存储规则引擎使用的脚本信息。

主要字段：

- `script_id`：脚本ID，主键
- `user_id`：用户ID
- `user_name`：用户昵称
- `scene_id`：关联场景ID
- `product_id`：产品ID
- `product_name`：产品名称
- `script_event`：脚本事件（1=设备上报，2=平台下发，3=设备上线，4=设备离线）
- `script_action`：脚本动作（1=消息重发，2=消息通知，3=Http推送，4=Mqtt桥接，5=数据库存储）
- `script_purpose`：脚本用途（1=数据流，2=触发器，3=执行动作）
- `script_order`：脚本执行顺序，值越大优先级越高
- `application_name`：应用名，后端、规则和脚本要统一
- `script_name`：脚本名
- `script_data`：脚本数据
- `script_type`：脚本类型（script=普通脚本，switch_script=选择脚本，boolean_script=条件脚本，for_script=数量循环脚本）
- `script_language`：脚本语言（groovy | qlexpress | js | python | lua | aviator | java）
- `enable`：是否生效（0-不生效，1-生效）

### 5. 脚本输入桥接关联表 (iot_script_bridge)

脚本输入桥接关联表存储脚本与桥接配置的关联信息。

主要字段：

- `script_id`：脚本ID，主键
- `bridge_id`：桥接配置id，主键

## 告警管理相关表

### 1. 设备告警表 (iot_alert)

设备告警表存储告警的基本信息。

主要字段：

- `alert_id`：告警ID，主键
- `alert_name`：告警名称
- `alert_level`：告警级别（1=提醒通知，2=轻微问题，3=严重警告）
- `status`：告警状态（1-启动，2-停止）
- `notify`：通知方式[1,2,3]
- `tenant_id`：租户id
- `tenant_name`：租户名称

### 2. 设备告警日志表 (iot_alert_log)

设备告警日志表存储告警的日志信息。

主要字段：

- `alert_log_id`：告警日志ID，主键
- `alert_name`：告警名称
- `alert_level`：告警级别（1=提醒通知，2=轻微问题，3=严重警告）
- `status`：处理状态（1=不需要处理,2=未处理,3=已处理）
- `serial_number`：设备编号
- `product_id`：产品ID
- `detail`：告警详情（对应物模型）
- `user_id`：用户id
- `device_name`：设备名称

### 3. 告警通知模版关联表 (iot_alert_notify_template)

告警通知模版关联表存储告警与通知模板的关联信息。

主要字段：

- `alert_id`：告警id，主键
- `notify_template_id`：通知模版id，主键

### 4. 告警场景表 (iot_alert_scene)

告警场景表存储告警与场景的关联信息。

主要字段：

- `alert_id`：告警ID，主键
- `scene_id`：场景ID，主键

## 代码生成相关表

### 1. 代码生成业务表 (gen_table)

代码生成业务表存储代码生成的业务信息。

主要字段：

- `table_id`：编号，主键
- `data_name`：数据源名称
- `table_name`：表名称
- `table_comment`：表描述
- `sub_table_name`：关联子表的表名
- `sub_table_fk_name`：子表关联的外键名
- `class_name`：实体类名称
- `tpl_category`：使用的模板
- `package_name`：生成包路径
- `module_name`：生成模块名
- `business_name`：生成业务名
- `function_name`：生成功能名
- `function_author`：生成功能作者
- `gen_type`：生成代码方式
- `gen_path`：生成路径

### 2. 代码生成业务表字段 (gen_table_column)

代码生成业务表字段存储代码生成的业务表字段信息。

主要字段：

- `column_id`：编号，主键
- `table_id`：归属表编号
- `column_name`：列名称
- `column_comment`：列描述
- `column_type`：列类型
- `java_type`：JAVA类型
- `java_field`：JAVA字段名
- `is_pk`：是否主键
- `is_increment`：是否自增
- `is_required`：是否必填
- `is_insert`：是否为插入字段
- `is_edit`：是否编辑字段
- `is_list`：是否列表字段
- `is_query`：是否查询字段
- `query_type`：查询方式
- `html_type`：显示类型
- `dict_type`：字典类型
- `sort`：排序

## 可视化大屏相关表

### 1. GoView项目表 (iot_goview_project)

 GoView项目表存储可视化大屏项目信息。

主要字段：

- `id`：主键
- `project_name`：项目名称
- `state`：项目状态
- `indexImage`：首页图片
- `remarks`：备注
- `create_time`：创建时间
- `create_by`：创建人ID
- `update_time`：更新时间
- `is_delete`：删除状态
- `tenant_id`：租户ID
- `tenant_name`：租户名称

### 2. GoView项目数据表 (iot_goview_project_data)

 GoView项目数据表存储可视化大屏项目的具体数据。

主要字段：

- `id`：主键
- `project_id`：项目ID
- `content`：存储数据
- `create_time`：创建时间
- `create_by`：创建人ID
- `update_time`：更新时间

## 通知管理相关表

### 1. 通知渠道表 (notify_channel)

通知渠道表存储通知渠道的基本信息。

主要字段：

- `id`：编号，主键
- `name`：通知名称
- `channel_type`：渠道类型（sms、email、voice、wechat、dingtalk、mqtt等）
- `provider`：服务商（alibaba、tencent、qq、163、mini_program、wecom_robot、wecom_apply、work、group_robot、web等）
- `config_content`：配置内容
- `tenant_id`：租户id
- `tenant_name`：租户名称

### 2. 通知模版表 (notify_template)

通知模版表存储通知模板的基本信息。

主要字段：

- `id`：编号，主键
- `name`：渠道名称
- `service_code`：业务编码(唯一启用)
- `channel_id`：通知渠道账号
- `channel_type`：渠道类型
- `provider`：服务商
- `msg_params`：模板配置参数
- `status`：是否启用（0-不启用 1-启用）
- `tenant_id`：租户id
- `tenant_name`：租户名称

### 3. 通知日志表 (notify_log)

通知日志表存储通知的日志信息。

主要字段：

- `id`：通知日志ID，主键
- `channel_id`：渠道编号
- `notify_template_id`：通知模版编号
- `msg_content`：消息内容
- `send_account`：发送账号
- `send_status`：发送状态
- `result_content`：返回内容
- `service_code`：业务编码(唯一启用)
- `tenant_id`：租户id
- `tenant_name`：租户名称
