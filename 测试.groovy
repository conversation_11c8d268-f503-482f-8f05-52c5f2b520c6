// 导入Hutool JSON处理工具类
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

// 1. 从消息上下文中获取MQTT主题和消息内容
String topic = msgContext.getTopic();
String payload = msgContext.getPayload();

// 2. 数据转换处理开始
msgContext.logger.info("数据转换处理")
// 3. 内容格式转换逻辑
JSONArray newArray = new JSONArray(); // 创建新数组存储转换后的数据
JSONArray jsonArray = JSONUtil.parseArray(payload); // 解析原始JSON数组
Boolean isSnMsg = false; // 标识是否为SN消息(非HMI消息)

// 遍历原始JSON数组中的每个元素
jsonArray.forEach(o -> {
    JSONObject obj = (JSONObject) o;
    String id = obj.getStr("id"); // 获取设备ID
    String hexString = Integer.toHexString(obj.getInt("value")); // 将数值转为16进制字符串
    
    // 判断是否为HMI设备消息
    if (id.contains("HMI")) {
        // 特殊处理的HMI设备ID列表
        if (id in ["HMI_30162", "HMI_30164", "HMI_30168", "HMI_30172", "HMI_30176"]) {
            JSONObject all = new JSONObject();
            all.put("id", id + "_str"); // 添加_str后缀的新ID
            // 格式化为XXXX-XX-XX格式的字符串
            all.put("value", "${hexString.substring(0, 4)}-${hexString.substring(4, 6)}-${hexString.substring(6, 8)}");
            newArray.add(all);
        } else {
            JSONObject all = new JSONObject();
            all.put("id", id + "_str"); // 添加_str后缀的新ID
            // 处理长度不足4的情况，否则格式化为X.XX.X格式
            String value = (hexString.length() < 4) ? "0" 
                : "${hexString.substring(0, 1)}.${hexString.substring(1, 3)}.${hexString.substring(3, 4)}";
            all.put("value", value);
            newArray.add(all);
        }
    } else {
        isSnMsg = true; // 标记为非HMI消息(SN消息)
    }
});

// 准备返回的新消息内容
String NewPayload = newArray.toString();
String NewTopic = topic;

// 如果是SN消息，则保持原始payload不变
if (isSnMsg) {
    NewPayload = payload;
}

// 设置返回的新主题和消息内容
msgContext.setTopic(NewTopic);
msgContext.setPayload(NewPayload);